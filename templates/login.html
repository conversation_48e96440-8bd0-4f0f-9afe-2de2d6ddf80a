{% extends "layout.html" %}

{% block content %}
<div class="flex flex-col justify-center items-center min-h-screen">
    <div class="mb-8 text-center">
        <a href="/qrcode" class="action-button success text-xl px-8 py-4 inline-flex items-center">
            <i class="fas fa-users mr-3 text-2xl"></i> Publikumsteilnahme
        </a>
    </div>

    <div class="glass-container p-8 rounded-xl max-w-md w-full">
        <h2 class="text-3xl font-bold mb-6 text-center text-white">Moderator Login</h2>

        {% if error %}
        <div class="bg-red-500 bg-opacity-30 text-white p-3 rounded-lg mb-6">
            {{ error }}
        </div>
        {% endif %}

        <form method="POST" action="{{ url_for('quiz.login') }}">
            <div class="mb-6">
                <label for="password" class="block text-white mb-2">Passwort</label>
                <input type="password" id="password" name="password"
                       class="w-full p-3 rounded-lg bg-black bg-opacity-30 text-white border border-gray-600 focus:border-primary focus:outline-none"
                       required>
            </div>

            <div class="flex justify-center">
                <button type="submit" class="action-button primary">
                    <i class="fas fa-sign-in-alt mr-2"></i> Anmelden
                </button>
            </div>
        </form>

        <div class="mt-8 text-center text-white text-sm">
            <p>Nur für Moderatoren. Publikumsteilnehmer können direkt auf die <a href="/audience" class="text-yellow-300 hover:text-yellow-100 hover:underline font-bold">Publikumsseite</a> zugreifen.</p>
        </div>
    </div>
</div>
{% endblock %}
