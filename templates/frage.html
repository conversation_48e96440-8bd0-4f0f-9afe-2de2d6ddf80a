{% extends "layout.html" %}

{% block content %}
<div class="question-container">
    <div class="flex items-center justify-between mb-6">
        <a href="/" class="text-primary hover:underline flex items-center transition-all duration-300 hover:translate-x-[-5px]">
            <i class="fas fa-arrow-left mr-2"></i> Zurück zur Übersicht
        </a>
        <div class="flex items-center">
            <i class="fas fa-{{ category.icon }} text-primary-light mr-3 text-2xl"></i>
            <h3 class="text-2xl font-semibold">{{ category.name }}</h3>
        </div>
    </div>

    <div id="question-display" class="animate-fade-in">
        <div class="question-text" id="question-text">
            {{ question.text }}
        </div>

        {% if question.question_image %}
        <div class="question-image mt-6 mb-6">
            <img src="{{ question.question_image }}" alt="Fragebild" class="rounded-lg max-w-full mx-auto" onclick="openFullscreenImage('{{ question.question_image }}')"
                 style="max-height: 300px; cursor: pointer;">
        </div>
        {% endif %}

        <div class="answers-container">
            {% for answer in question.answers %}
            <button class="answer-button animate-slide-in" id="answer-{{ loop.index0 }}" data-index="{{ loop.index0 }}" style="animation-delay: {{ loop.index0 * 0.1 }}s">
                <span class="answer-letter">{{ ['A', 'B', 'C'][loop.index0] }}</span>
                {{ answer }}
            </button>
            {% endfor %}
        </div>

        <div class="flex justify-center mt-8">
            <button class="action-button primary hidden" id="reveal-button">Antwort aufdecken</button>
            <button class="action-button hidden" id="next-player-button">Nächster Spieler</button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const questionText = document.getElementById('question-text');
        const answerButtons = document.querySelectorAll('.answer-button');
        const revealButton = document.getElementById('reveal-button');
        const nextPlayerButton = document.getElementById('next-player-button');

        let selectedIndex = null;
        let answeredCorrectly = false;

        // Add event listeners to answer buttons
        answerButtons.forEach(button => {
            button.addEventListener('click', function() {
                if (selectedIndex === null) {
                    // Remove active class from all buttons
                    answerButtons.forEach(btn => btn.classList.remove('active'));

                    // Add active class to selected button
                    this.classList.add('active');

                    // Store selected index
                    selectedIndex = parseInt(this.dataset.index);

                    // Show reveal button
                    revealButton.classList.remove('hidden');
                }
            });
        });

        // Add event listener to reveal button
        revealButton.addEventListener('click', function() {
            if (selectedIndex !== null) {
                // Submit answer to API
                submitAnswer(selectedIndex);
            }
        });

        // Add event listener to next player button
        nextPlayerButton.addEventListener('click', function() {
            // Switch to next player and go back to categories
            fetch('/api/next-player', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({})
            })
            .then(response => response.json())
            .then(data => {
                window.location.href = '/';
            })
            .catch(error => {
                console.error('Error switching player:', error);
            });
        });

        function submitAnswer(answerIndex) {
            fetch('/api/answer', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    category: '{{ slug }}',
                    answer: answerIndex
                })
            })
            .then(response => response.json())
            .then(data => {
                // Show correct/incorrect feedback
                const correctIndex = data.correct_index;

                // Mark correct and incorrect answers
                answerButtons.forEach((button, index) => {
                    if (index === correctIndex) {
                        button.classList.add('correct');
                    } else if (index === selectedIndex && index !== correctIndex) {
                        button.classList.add('incorrect');
                    }
                });

                // Hide reveal button and show next player button
                revealButton.classList.add('hidden');
                nextPlayerButton.classList.remove('hidden');

                // Create result overlay
                const resultOverlay = document.createElement('div');
                resultOverlay.className = 'result-overlay';

                if (data.correct) {
                    // Show success message and confetti
                    resultOverlay.innerHTML = `
                        <div class="result-content correct-result">
                            <div class="result-icon"><i class="fas fa-check-circle"></i></div>
                            <div class="result-text">Richtig!</div>
                        </div>
                    `;
                    createConfetti();
                } else {
                    // Show failure message with sad face
                    resultOverlay.innerHTML = `
                        <div class="result-content incorrect-result">
                            <div class="result-icon"><i class="fas fa-sad-tear"></i></div>
                            <div class="result-text">Leider falsch!</div>
                        </div>
                    `;
                }

                // Create explanation section if available
                if (data.explanation || data.image) {
                    const explanationSection = document.createElement('div');
                    explanationSection.className = 'explanation-section glass-container mt-8 p-6 rounded-xl';

                    let explanationHTML = '<h4 class="text-xl font-bold text-white mb-4">Erklärung:</h4>';

                    if (data.image) {
                        explanationHTML += `
                            <div class="explanation-image mb-4">
                                <img src="${data.image}" alt="Erklärungsbild" class="rounded-lg max-w-full mx-auto" onclick="openFullscreenImage('${data.image}')">
                            </div>
                        `;
                    }

                    if (data.explanation) {
                        explanationHTML += `
                            <div class="explanation-text text-white">
                                ${data.explanation}
                            </div>
                        `;
                    }

                    explanationSection.innerHTML = explanationHTML;
                    document.querySelector('.question-container').appendChild(explanationSection);
                }

                // Add audience results if available
                if (data.audience_results && data.audience_results.success) {
                    const audienceResults = data.audience_results;
                    const totalAnswers = audienceResults.total_answers;

                    if (totalAnswers > 0) {
                        const correctAnswers = audienceResults.correct_answers;
                        const correctPercentage = Math.round((correctAnswers / totalAnswers) * 100);
                        const answerCounts = audienceResults.answer_counts;

                        // Create audience results element
                        const audienceResultsElement = document.createElement('div');
                        audienceResultsElement.className = 'audience-results mt-6 p-4 rounded-lg bg-opacity-20 bg-black';

                        // Calculate percentages for each answer
                        const percentA = totalAnswers > 0 ? Math.round((answerCounts[0] / totalAnswers) * 100) : 0;
                        const percentB = totalAnswers > 0 ? Math.round((answerCounts[1] / totalAnswers) * 100) : 0;
                        const percentC = totalAnswers > 0 ? Math.round((answerCounts[2] / totalAnswers) * 100) : 0;

                        audienceResultsElement.innerHTML = `
                            <h4 class="text-lg font-bold text-white mb-2">Publikumsergebnisse</h4>
                            <div class="flex justify-between items-center mb-3">
                                <div class="text-white text-left">
                                    <div class="font-bold text-xl">${totalAnswers} <span class="text-sm font-normal">Teilnehmer</span></div>
                                    <div class="font-bold text-xl">${correctAnswers} <span class="text-sm font-normal">richtige Antworten</span></div>
                                </div>
                                <div class="text-right">
                                    <div class="font-bold text-xl text-green-400">${correctPercentage}% <span class="text-sm font-normal text-white">Erfolgsquote</span></div>
                                    <div class="font-bold text-xl text-yellow-300">+${audienceResults.correct_answers * 100} <span class="text-sm font-normal text-white">Punkte</span></div>
                                </div>
                            </div>

                            <h5 class="text-md font-semibold text-white mb-2">Antwortverteilung:</h5>
                            <div class="flex justify-between space-x-2 mt-3">
                                <div class="answer-stat ${correctIndex === 0 ? 'correct-stat' : ''}" style="width: ${Math.max(percentA, 5)}%">
                                    <span class="answer-letter">A</span>
                                    <span class="text-white">${answerCounts[0]} (${percentA}%)</span>
                                </div>
                                <div class="answer-stat ${correctIndex === 1 ? 'correct-stat' : ''}" style="width: ${Math.max(percentB, 5)}%">
                                    <span class="answer-letter">B</span>
                                    <span class="text-white">${answerCounts[1]} (${percentB}%)</span>
                                </div>
                                <div class="answer-stat ${correctIndex === 2 ? 'correct-stat' : ''}" style="width: ${Math.max(percentC, 5)}%">
                                    <span class="answer-letter">C</span>
                                    <span class="text-white">${answerCounts[2]} (${percentC}%)</span>
                                </div>
                            </div>
                        `;

                        // Add to the question container
                        document.querySelector('.question-container').appendChild(audienceResultsElement);
                    }
                }

                document.querySelector('.question-container').appendChild(resultOverlay);

                // Remove the overlay after 3 seconds
                setTimeout(() => {
                    resultOverlay.classList.add('fade-out');
                    setTimeout(() => {
                        resultOverlay.remove();
                    }, 500);
                }, 3000);
            })
            .catch(error => {
                console.error('Error submitting answer:', error);
            });
        }

        function createConfetti() {
            const confettiCount = 100;
            const container = document.querySelector('body');

            for (let i = 0; i < confettiCount; i++) {
                const confetti = document.createElement('div');
                confetti.className = 'confetti';

                // Random position, color, and delay
                const size = Math.random() * 10 + 5;
                const left = Math.random() * 100;
                const colors = ['#4f46e5', '#ec4899', '#10b981', '#f59e0b', '#3b82f6'];
                const color = colors[Math.floor(Math.random() * colors.length)];
                const delay = Math.random() * 3;

                confetti.style.width = `${size}px`;
                confetti.style.height = `${size}px`;
                confetti.style.left = `${left}%`;
                confetti.style.backgroundColor = color;
                confetti.style.animationDelay = `${delay}s`;

                container.appendChild(confetti);

                // Remove confetti after animation
                setTimeout(() => {
                    confetti.remove();
                }, 3000 + (delay * 1000));
            }
        }
    });
</script>
{% endblock %}
