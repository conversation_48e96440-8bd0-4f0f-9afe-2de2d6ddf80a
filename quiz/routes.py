from flask import Blueprint, jsonify, render_template, current_app, request, redirect, url_for, session, flash
from functools import wraps
from .loader import load_data, get_game_state, save_game_state, reset_game
from .qrcode_util import generate_qrcode
import os
import uuid
import hashlib

quiz_bp = Blueprint('quiz', __name__)

# Load quiz data from YAML file
data_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'fragen.yaml')
quiz_data = load_data(data_file)

# Password configuration
MODERATOR_PASSWORD = 'quiz2023'  # Change this to your desired password

# Password hash function
def hash_password(password):
    return hashlib.sha256(password.encode()).hexdigest()

# Check if the user is logged in
def is_logged_in():
    return session.get('logged_in', False)

# Login required decorator
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not is_logged_in():
            return redirect(url_for('quiz.login', next=request.url))
        return f(*args, **kwargs)
    return decorated_function

@quiz_bp.route('/login', methods=['GET', 'POST'])
def login():
    """Login page for moderators"""
    error = None
    next_url = request.args.get('next', url_for('quiz.index'))

    if request.method == 'POST':
        password = request.form.get('password', '')

        if password == MODERATOR_PASSWORD:
            session['logged_in'] = True
            return redirect(next_url)
        else:
            error = 'Falsches Passwort. Bitte versuche es erneut.'

    return render_template('login.html', error=error)

@quiz_bp.route('/logout')
def logout():
    """Logout the moderator"""
    session.pop('logged_in', None)
    return redirect(url_for('quiz.login'))

@quiz_bp.route('/')
def index():
    """Render the categories wall or redirect to login"""
    # Check if user is logged in
    if not is_logged_in():
        return redirect(url_for('quiz.login'))

    # Original index function
    """Render the categories wall"""
    # Get the current game state
    game_state = get_game_state()

    # Filter out special categories and used categories
    categories = {}
    for slug, category in quiz_data.items():
        if slug not in ['masterfrage', 'schaetzfrage']:
            cat_dict = category.to_dict()
            cat_dict['used'] = game_state.is_category_used(slug)
            categories[slug] = cat_dict

    # Check if all categories have been used
    if game_state.all_categories_used(len(categories)):
        game_state.game_phase = 'master'
        game_state.master_phase = 'bet'  # Start with betting phase
        game_state.current_player = 0  # Start with player 1
        save_game_state(game_state)
        return redirect(url_for('quiz.master_question'))

    return render_template('index.html',
                           categories=categories,
                           game_state=game_state.to_dict(),
                           current_player=game_state.players[game_state.current_player])

@quiz_bp.route('/frage/<slug>')
@login_required
def frage(slug):
    """Render the question view for a specific category"""
    # Get the current game state
    game_state = get_game_state()

    # Check if the category exists
    if slug not in quiz_data:
        return render_template('error.html', message="Kategorie nicht gefunden"), 404

    # Check if the category has already been used
    if game_state.is_category_used(slug):
        return redirect(url_for('quiz.index'))

    # Set the game phase to question and mark the category as used for audience participation
    # This allows audience members to see the question immediately
    game_state.game_phase = 'question'
    game_state.mark_category_used(slug)
    save_game_state(game_state)

    # Get the category and question
    category = quiz_data[slug]
    question = category.question.to_dict() if hasattr(category, 'question') else None

    return render_template('frage.html',
                           category=category.to_dict(),
                           question=question,
                           slug=slug,
                           game_state=game_state.to_dict(),
                           current_player=game_state.players[game_state.current_player])

@quiz_bp.route('/master')
@login_required
def master_question():
    """Render the master question view"""
    # Get the current game state
    game_state = get_game_state()

    # Make sure we're in the master phase
    if game_state.game_phase != 'master':
        return redirect(url_for('quiz.index'))

    # Get the master question
    master_data = quiz_data.get('masterfrage', None)
    if not master_data:
        return render_template('error.html', message="Masterfrage nicht gefunden"), 404

    # Determine which template to use based on the master phase
    if game_state.master_phase == 'bet':
        template = 'master_bet.html'
    elif game_state.master_phase == 'answer':
        template = 'master_answer.html'
    elif game_state.master_phase == 'overview':
        template = 'master_overview.html'
    else:
        template = 'master_bet.html'

    return render_template(template,
                           master=master_data,
                           game_state=game_state.to_dict(),
                           current_player=game_state.players[game_state.current_player])

@quiz_bp.route('/tiebreaker')
@login_required
def tiebreaker():
    """Render the tiebreaker question view"""
    # Get the current game state
    game_state = get_game_state()

    # Check if we need a tiebreaker
    if not game_state.is_tie() or game_state.game_phase != 'finished':
        return redirect(url_for('quiz.results'))

    # Get the tiebreaker question
    tiebreaker_data = quiz_data.get('schaetzfrage', None)
    if not tiebreaker_data:
        return render_template('error.html', message="Schätzfrage nicht gefunden"), 404

    game_state.game_phase = 'tiebreaker'
    save_game_state(game_state)

    return render_template('tiebreaker.html',
                           tiebreaker=tiebreaker_data,
                           game_state=game_state.to_dict(),
                           current_player=game_state.players[game_state.current_player])

@quiz_bp.route('/results')
@login_required
def results():
    """Render the final results view"""
    # Get the current game state
    game_state = get_game_state()

    # Make sure the game is finished
    if game_state.game_phase not in ['finished', 'tiebreaker']:
        return redirect(url_for('quiz.index'))

    winner = game_state.get_winner()

    return render_template('results.html',
                           game_state=game_state.to_dict(),
                           winner=winner,
                           players=game_state.players)

@quiz_bp.route('/reset')
@login_required
def reset():
    """Reset the game"""
    reset_game()
    return redirect(url_for('quiz.index'))

# API Endpoints

@quiz_bp.route('/api/kategorien')
def api_kategorien():
    """API endpoint to get all categories"""
    game_state = get_game_state()

    categories = {}
    for slug, category in quiz_data.items():
        if slug not in ['masterfrage', 'schaetzfrage']:
            cat_dict = category.to_dict()
            cat_dict['used'] = game_state.is_category_used(slug)
            categories[slug] = cat_dict

    return jsonify({
        'categories': categories,
        'game_state': game_state.to_dict()
    })

@quiz_bp.route('/api/frage/<slug>')
def api_frage(slug):
    """API endpoint to get the question for a category"""
    if slug not in quiz_data:
        return jsonify({"error": "Kategorie nicht gefunden"}), 404

    category = quiz_data[slug]
    question = category.question.to_dict() if hasattr(category, 'question') else None

    if not question:
        return jsonify({"error": "Keine Frage verfügbar"}), 404

    return jsonify(question)

@quiz_bp.route('/api/answer', methods=['POST'])
@login_required
def api_answer():
    """API endpoint to submit an answer"""
    data = request.json
    slug = data.get('category', '')
    answer_index = data.get('answer', -1)

    if slug not in quiz_data:
        return jsonify({"error": "Kategorie nicht gefunden"}), 404

    # Get the current game state
    game_state = get_game_state()

    # Get the category and question
    category = quiz_data[slug]
    question = category.question if hasattr(category, 'question') else None

    if not question:
        return jsonify({"error": "Keine Frage verfügbar"}), 404

    # Check if the answer is correct
    is_correct = (answer_index == question.correct_index)

    # Award points if correct
    if is_correct:
        game_state.add_score(500, slug)

    # Mark the category as used now that the answer is revealed
    game_state.mark_category_used(slug)

    # Add the category to answered_categories list
    if slug not in game_state.answered_categories:
        game_state.answered_categories.append(slug)

    # Process all pending audience answers for this category
    audience_results = game_state.process_audience_answers(slug)

    save_game_state(game_state)

    return jsonify({
        'correct': is_correct,
        'correct_index': question.correct_index,
        'explanation': question.explanation,
        'image': question.answer_image,  # Antwortbild für Erklärung
        'audience_results': audience_results,
        'game_state': game_state.to_dict()
    })

@quiz_bp.route('/api/next-player', methods=['POST'])
@login_required
def api_next_player():
    """API endpoint to switch to the next player"""
    # Get the current game state
    game_state = get_game_state()

    # Switch to the next player
    game_state.switch_player()
    save_game_state(game_state)

    return jsonify({
        'game_state': game_state.to_dict(),
        'current_player': game_state.players[game_state.current_player]
    })

@quiz_bp.route('/api/master-bet', methods=['POST'])
@login_required
def api_master_bet():
    """API endpoint to submit a bet for the master question"""
    data = request.json
    bet = data.get('bet', 0)

    # Get the current game state
    game_state = get_game_state()

    # Set the bet
    game_state.set_master_bet(bet)

    # Check if both players have placed their bets
    if game_state.master_bets[0] > 0 and game_state.master_bets[1] > 0:
        # Move to the answer phase
        game_state.master_phase = 'answer'
        game_state.current_player = 0  # Reset to player 1 for answering
    else:
        # Switch to the next player for betting
        game_state.switch_player()

    save_game_state(game_state)

    return jsonify({
        'game_state': game_state.to_dict()
    })

@quiz_bp.route('/api/master-answer', methods=['POST'])
@login_required
def api_master_answer():
    """API endpoint to submit an answer for the master question"""
    data = request.json
    answer_index = data.get('answer', -1)

    # Get the current game state
    game_state = get_game_state()

    # Set the answer
    game_state.set_master_answer(answer_index)

    # Get the master question
    master_data = quiz_data.get('masterfrage', None)
    if not master_data:
        return jsonify({"error": "Masterfrage nicht gefunden"}), 404

    # Get the correct index
    from quiz.loader import Question

    # For the masterfrage, the korrekt field is directly in the YAML data
    correct_index = master_data.get('korrekt', 0)

    # If not found, try to get it from the question object
    if correct_index == 0 and 'question' in master_data:
        question = master_data['question']
        if isinstance(question, Question):
            correct_index = question.correct_index

    # Check if both players have answered
    if game_state.master_answers[0] is not None and game_state.master_answers[1] is not None:
        # Move to the overview phase before revealing the answer
        game_state.master_phase = 'overview'
    else:
        # Switch to the next player
        game_state.switch_player()

    save_game_state(game_state)

    return jsonify({
        'game_state': game_state.to_dict()
    })

@quiz_bp.route('/api/tiebreaker-guess', methods=['POST'])
@login_required
def api_tiebreaker_guess():
    """API endpoint to submit a guess for the tiebreaker question"""
    data = request.json
    guess = data.get('guess', 0)

    # Get the current game state
    game_state = get_game_state()

    # Set the guess
    game_state.set_tiebreaker_guess(guess)

    # Get the tiebreaker question
    tiebreaker_data = quiz_data.get('schaetzfrage', None)
    if not tiebreaker_data:
        return jsonify({"error": "Schätzfrage nicht gefunden"}), 404

    # Check if both players have guessed
    if game_state.tiebreaker_guesses[0] is not None and game_state.tiebreaker_guesses[1] is not None:
        # Process the results
        correct_answer = tiebreaker_data.get('answer', 0)
        game_state.process_tiebreaker_result(correct_answer)
        game_state.game_phase = 'finished'
    else:
        # Switch to the next player
        game_state.switch_player()

    save_game_state(game_state)

    return jsonify({
        'game_state': game_state.to_dict()
    })

# Audience participation routes

@quiz_bp.route('/audience')
def audience():
    """Render the audience participation page"""
    # Get the current game state
    game_state = get_game_state()

    return render_template('audience.html',
                           game_state=game_state.to_dict())

@quiz_bp.route('/audience/master')
def audience_master():
    """Render the audience master question page"""
    # Get the current game state
    game_state = get_game_state()

    # Check if we're in the master phase
    if game_state.game_phase != 'master':
        return redirect(url_for('quiz.audience'))

    # Check if the audience member is registered
    if 'audience_id' not in session:
        return redirect(url_for('quiz.audience'))

    audience_id = session['audience_id']
    if audience_id not in game_state.audience_members:
        # Try to re-register the audience member
        success = game_state.add_audience_member(audience_id)
        if not success:
            return redirect(url_for('quiz.audience'))
        save_game_state(game_state)

    # Get the master question
    master_data = quiz_data.get('masterfrage', None)
    if not master_data:
        return render_template('error.html', message="Masterfrage nicht gefunden"), 404

    return render_template('audience_master.html',
                           master=master_data,
                           game_state=game_state.to_dict())

@quiz_bp.route('/audience/results')
def audience_results():
    """Render the audience results page"""
    # Get the current game state
    game_state = get_game_state()

    # Make sure the game is finished
    if game_state.game_phase != 'finished':
        return redirect(url_for('quiz.audience'))

    # Check if the audience member is registered
    if 'audience_id' not in session:
        return redirect(url_for('quiz.audience'))

    audience_id = session['audience_id']
    if audience_id not in game_state.audience_members:
        return redirect(url_for('quiz.audience'))

    audience_member = game_state.audience_members[audience_id]
    winner = game_state.get_winner()

    return render_template('audience_results.html',
                           game_state=game_state.to_dict(),
                           audience_member=audience_member,
                           winner=winner,
                           players=game_state.players)

@quiz_bp.route('/audience/question')
def audience_question():
    """Render the audience question page"""
    # Get the current game state
    game_state = get_game_state()

    # Check if we're in the master phase - redirect to master question page
    if game_state.game_phase == 'master':
        return redirect(url_for('quiz.audience_master'))

    # Check if the game is finished - redirect to results page
    if game_state.game_phase == 'finished':
        return redirect(url_for('quiz.audience_results'))

    # Check if the audience member is registered
    if 'audience_id' not in session:
        print("AUDIENCE - No audience_id in session, redirecting to audience page")
        return redirect(url_for('quiz.audience'))

    audience_id = session['audience_id']
    if audience_id not in game_state.audience_members:
        print(f"AUDIENCE - audience_id {audience_id} not in game_state.audience_members, redirecting to audience page")
        # Try to re-register the audience member
        success = game_state.add_audience_member(audience_id)
        if not success:
            return redirect(url_for('quiz.audience'))
        save_game_state(game_state)

    audience_member = game_state.audience_members[audience_id]

    # Get the current question
    current_question = None
    category = None
    question = None
    question_revealed = False

    # Debug output for game state
    print(f"AUDIENCE - Game phase: {game_state.game_phase}, Used categories: {len(game_state.used_categories)}")
    print(f"AUDIENCE - Session ID: {session.get('audience_id', 'not_set')}")
    if game_state.used_categories:
        print(f"AUDIENCE - Last category: {game_state.used_categories[-1]}")
    else:
        print("AUDIENCE - No categories used yet")

    # Check if there's an active question
    # We need to check if there are used categories, regardless of the game phase
    # This is critical for audience participation
    if game_state.used_categories:
        # Get the most recently used category
        current_question = game_state.used_categories[-1]
        print(f"Current question category: {current_question}")

        # Check if the category exists in quiz data
        if current_question in quiz_data:
            category = quiz_data[current_question]

            # Check if the category has a question
            if hasattr(category, 'question'):
                question = category.question.to_dict()
                print(f"Question found: {question['text'][:30]}...")

                # Check if the question has been revealed (if it's in answered_categories)
                if current_question in game_state.answered_categories:
                    question_revealed = True

                # Debug output
                print(f"Audience question: {current_question}, revealed: {question_revealed}")

    return render_template('audience_question.html',
                           game_state=game_state.to_dict(),
                           audience_member=audience_member,
                           current_question=current_question,
                           category=category.to_dict() if category else None,
                           question=question,
                           question_revealed=question_revealed)

@quiz_bp.route('/qrcode')
def qrcode():
    """Render a page with the QR code for audience participation"""
    # Get the current game state
    game_state = get_game_state()

    # Generate the QR code for the audience participation page
    host = request.host_url.rstrip('/')
    audience_url = f"{host}/audience"
    qr_code_data = generate_qrcode(audience_url)

    return render_template('qrcode.html',
                           qr_code=qr_code_data,
                           audience_url=audience_url,
                           game_state=game_state.to_dict())

@quiz_bp.route('/api/audience-register', methods=['POST'])
def api_audience_register():
    """API endpoint to register an audience member"""
    # Get the current game state
    game_state = get_game_state()

    # Generate a unique ID for the audience member
    # In a real app, you might use a session ID or user account
    if 'audience_id' not in session:
        session['audience_id'] = str(uuid.uuid4())
    audience_id = session['audience_id']

    # Register the audience member
    success = game_state.add_audience_member(audience_id)

    # Save the game state
    save_game_state(game_state)

    return jsonify({
        'success': success,
        'game_state': game_state.to_dict()
    })

@quiz_bp.route('/api/audience-answer', methods=['POST'])
def api_audience_answer():
    """API endpoint to submit an audience answer"""
    data = request.json
    category_slug = data.get('category', '')
    answer_index = data.get('answer', -1)

    # Get the current game state
    game_state = get_game_state()

    # Check if the audience member is registered
    if 'audience_id' not in session:
        return jsonify({"error": "Nicht registriert"}), 400

    audience_id = session['audience_id']

    # Store the answer (but don't process it yet)
    result = game_state.store_audience_answer(audience_id, category_slug, answer_index)

    # Save the game state
    save_game_state(game_state)

    return jsonify(result)

@quiz_bp.route('/api/current-question')
def api_current_question():
    """API endpoint to get the current question status"""
    # Get the current game state
    game_state = get_game_state()

    # Check if we're in the master phase
    if game_state.game_phase == 'master':
        # Get the master question
        master_data = quiz_data.get('masterfrage', None)
        if master_data and hasattr(master_data, 'question'):
            question = master_data.question.to_dict()
            return jsonify({
                'success': True,
                'current_player': game_state.current_player,
                'game_phase': game_state.game_phase,
                'master_phase': game_state.master_phase,
                'current_question': 'masterfrage',
                'question': question,
                'question_revealed': game_state.game_phase == 'finished'
            })

    # Get the current question
    current_question = None
    category = None
    question = None
    question_revealed = False

    # Debug output for game state
    print(f"API - Game phase: {game_state.game_phase}, Used categories: {len(game_state.used_categories)}")
    print(f"API - Session ID: {session.get('audience_id', 'not_set')}")
    if game_state.used_categories:
        print(f"API - Last category: {game_state.used_categories[-1]}")
    else:
        print("API - No categories used yet")

    # Check if there's an active question
    # We need to check if there are used categories, regardless of the game phase
    # This is critical for audience participation
    if game_state.used_categories:
        # Get the most recently used category
        current_question = game_state.used_categories[-1]
        print(f"API - Current question category: {current_question}")

        # Check if the category exists in quiz data
        if current_question in quiz_data:
            category = quiz_data[current_question]

            # Check if the category has a question
            if hasattr(category, 'question'):
                question = category.question.to_dict()
                print(f"API - Question found: {question['text'][:30]}...")

                # Check if the question has been revealed (if it's in answered_categories)
                if current_question in game_state.answered_categories:
                    question_revealed = True

                # Debug output
                print(f"API current question: {current_question}, revealed: {question_revealed}")

    # Include audience statistics if the question has been revealed
    audience_results = None
    if question_revealed and current_question:
        # Get audience statistics for this question
        audience_stats = game_state.process_audience_answers(current_question)
        if audience_stats.get('success', False):
            audience_results = audience_stats

    # Add explanation and image if available
    explanation = None
    image = None
    if question_revealed and current_question and question:
        # Get the question object
        category_obj = quiz_data.get(current_question)
        if category_obj and hasattr(category_obj, 'question'):
            explanation = category_obj.question.explanation if hasattr(category_obj.question, 'explanation') else None
            image = category_obj.question.answer_image if hasattr(category_obj.question, 'answer_image') else None

    return jsonify({
        'success': True,
        'current_player': game_state.current_player,
        'game_phase': game_state.game_phase,
        'current_question': current_question,
        'category': category.to_dict() if category else None,
        'question': question,
        'question_revealed': question_revealed,
        'used_categories': game_state.used_categories,
        'audience_results': audience_results,
        'explanation': explanation,
        'image': image
    })

@quiz_bp.route('/api/debug')
def api_debug():
    """Debug endpoint to get the full game state"""
    # Get the current game state
    game_state = get_game_state()

    # Return the full game state
    return jsonify({
        'success': True,
        'game_state': game_state.to_dict(),
        'session_id': session.get('audience_id', 'not_set'),
        'is_logged_in': is_logged_in()
    })

@quiz_bp.route('/api/audience-master-bet', methods=['POST'])
def api_audience_master_bet():
    """API endpoint to submit an audience bet for the master question"""
    data = request.json
    player_index = data.get('player', 0)
    bet = data.get('bet', 0)
    answer_index = data.get('answer', -1)

    # Get the current game state
    game_state = get_game_state()

    # Check if the audience member is registered
    if 'audience_id' not in session:
        return jsonify({"error": "Nicht registriert"}), 400

    audience_id = session['audience_id']

    # Store the bet and answer
    result = game_state.store_audience_master_bet(audience_id, player_index, bet, answer_index)

    # Save the game state
    save_game_state(game_state)

    return jsonify(result)

@quiz_bp.route('/api/master-reveal', methods=['POST', 'GET'])
@login_required
def api_master_reveal():
    """API endpoint to reveal the master question answer or get explanation"""
    # Get the current game state
    game_state = get_game_state()

    # Handle GET request to fetch explanation
    if request.method == 'GET':
        # Get the master question
        master_data = quiz_data.get('masterfrage', None)
        if not master_data:
            return jsonify({"error": "Masterfrage nicht gefunden"}), 404

        # Get the explanation and image
        master_question = master_data['question']

        return jsonify({
            'explanation': master_question.explanation if hasattr(master_question, 'explanation') else None,
            'image': master_question.answer_image if hasattr(master_question, 'answer_image') else None
        })

    # For POST requests, continue with revealing the answer
    # Make sure we're in the master overview phase
    if game_state.game_phase != 'master' or game_state.master_phase != 'overview':
        return jsonify({"error": "Nicht in der Übersichtsphase"}), 400

    # Get the master question
    master_data = quiz_data.get('masterfrage', None)
    if not master_data:
        return jsonify({"error": "Masterfrage nicht gefunden"}), 404

    # Get the correct index
    from quiz.loader import Question

    # For the masterfrage, the korrekt field is directly in the YAML data
    correct_index = master_data.get('korrekt', 0)

    # If not found, try to get it from the question object
    if correct_index == 0 and 'question' in master_data:
        question = master_data['question']
        if isinstance(question, Question):
            correct_index = question.correct_index

    # Process the results
    game_state.process_master_result(correct_index)
    game_state.game_phase = 'finished'

    save_game_state(game_state)

    # Include explanation and image in the response for the results page
    master_question = master_data['question']

    return jsonify({
        'success': True,
        'game_state': game_state.to_dict(),
        'explanation': master_question.explanation if hasattr(master_question, 'explanation') else None,
        'image': master_question.answer_image if hasattr(master_question, 'answer_image') else None
    })

@quiz_bp.route('/api/game-state')
def api_game_state():
    """API endpoint to get the current game state"""
    # Get the current game state
    game_state = get_game_state()

    return jsonify({
        'success': True,
        'game_state': game_state.to_dict()
    })
